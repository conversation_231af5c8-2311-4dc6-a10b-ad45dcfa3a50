const mysql = require("mysql2");
const nodemailer = require("nodemailer");
const cron = require("node-cron");
require("dotenv").config();

// DB Connection
const db = mysql.createPool({
  connectionLimit: 10,
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  port: process.env.DB_PORT,
  charset: "UTF8_GENERAL_CI",
});

console.log("Database Config:");
console.log("Host:", process.env.DB_HOST);
console.log("User:", process.env.DB_USER);
console.log("Database:", process.env.DB_NAME);
console.log("Port:", process.env.DB_PORT);
console.log("SMTP-User:", process.env.SMTP_USER);


// Email Sender
async function sendEmail({ to,cc, subject, html }) {
  const transporter = nodemailer.createTransport({
    service: "gmail",
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASSWORD,
    },
    secure: true,
    port: 465,
  });

  await transporter.sendMail({
    from: `"WFMS" <${process.env.SMTP_USER}>`,
    to,
    cc,
    subject,
    html,
  });
}

async function isHolidayToday() {
  const todayQuery = `SELECT 1 FROM holidays WHERE date = CURDATE() LIMIT 1`;
  const result = await queryDb(todayQuery);
  return result.length > 0;
}


function queryDb(query) {
  return new Promise((resolve, reject) => {
    db.query(query, (err, results) => {
      if (err) return reject(err);
      resolve(results);
    });
  });
}

function formatMinutesToHhMm(minutes) {
  const hrs = Math.floor(minutes / 60);
  const mins = Math.round(minutes % 60);
  return `${hrs}h ${mins}m`;
}


async function generateTimeLogAndLeaveReport(currentDate) {
  const timelogQuery = `

SELECT 
  e.name AS employee_name,
  u.name AS reporting_manager,
  pt.total_minutes,
  t.team_name,
  (
    SELECT COALESCE(SUM(b2.total_minutes), 0)
    FROM project_time_log_breaks b2
    WHERE b2.project_time_log_id = pt.id
  ) AS total_break_minutes,
  (
    SELECT 
        ROUND((
          SUM(CAST(pt4.total_minutes AS UNSIGNED)) - 
          IFNULL((
            SELECT SUM(CAST(b3.total_minutes AS UNSIGNED)) 
            FROM project_time_log_breaks b3
            WHERE b3.project_time_log_id IN (
              SELECT pt5.id 
              FROM project_time_logs pt5 
              WHERE pt5.task_id = pt.task_id AND pt5.user_id = e.id
            )
          ), 0)
        ) / 60, 2)
    FROM 
        project_time_logs pt4
    WHERE 
        pt4.task_id = pt.task_id AND pt4.user_id = e.id
  ) AS user_task_hours,

  pt.start_time,
  pt.end_time

FROM 
  project_time_logs pt
JOIN users e ON pt.user_id = e.id AND e.status = 'active'
JOIN employee_details ed ON e.id = ed.user_id
JOIN users u ON ed.reporting_to = u.id
LEFT JOIN teams t ON ed.department_id = t.id 

WHERE 
  DATE(pt.start_time) = CURDATE()  AND  DATE(pt.end_time) = CURDATE()
  AND e.id NOT IN (
    SELECT user_id 
    FROM leaves 
    WHERE leave_date = CURDATE() AND status = 'approved'
  )

GROUP BY
  pt.id, e.name, u.name, pt.total_minutes, pt.start_time, pt.end_time, pt.task_id, t.team_name

ORDER BY 
  t.team_name, u.name, e.name;

  `;

  const leaveQuery = `
    SELECT 
      u.name AS employee_name,
      u.email AS employee_email,
      manager.name AS reporting_manager,
      manager.email AS manager_email,
      t.team_name AS department_name
    FROM leaves l
    JOIN users u ON l.user_id = u.id
    JOIN employee_details ed ON u.id = ed.user_id
    LEFT JOIN users manager ON ed.reporting_to = manager.id
    LEFT JOIN teams t ON ed.department_id = t.id
    WHERE l.leave_date = CURDATE() AND l.status = 'approved'
    ORDER BY department_name ASC;
  `;

const noTimeLogSql = `
 SELECT 
    u.name AS employee_name,
    u.email AS employee_email,
    manager.name AS reporting_manager,
    manager.email AS manager_email,
    t.team_name AS department_name
  FROM users u
  JOIN employee_details ed ON u.id = ed.user_id
  LEFT JOIN users manager ON ed.reporting_to = manager.id
  LEFT JOIN teams t ON ed.department_id = t.id
  WHERE u.status = 'active'
    AND u.id NOT IN (
      SELECT user_id 
      FROM project_time_logs 
      WHERE DATE(start_time) = CURDATE()
    )
    AND u.id NOT IN (
      SELECT user_id 
      FROM leaves 
      WHERE leave_date = CURDATE() AND status = 'approved'
    )
    ORDER BY  manager.name ASC;
;
`;


  const [timelogResults, leaveResults,noTimeLogResults] = await Promise.all([
    queryDb(timelogQuery),
    queryDb(leaveQuery),
    queryDb(noTimeLogSql),

  ]);

  return generateReport(timelogResults, leaveResults,noTimeLogResults,currentDate );
}



function generateReport(results, leaveResults,noTimeLogResults,currentDate) {
  const teamMap = {}; // Team-wise grouping

  results.forEach((row) => {
    if (!row.end_time) return;

    // Calculate work minutes by subtracting break minutes from total minutes
    // Use the same logic as in MONTHLY.js
    const totalMins = parseInt(row.total_minutes, 10) || 0;
    const breakMins = parseInt(row.total_break_minutes, 10) || 0;
    const workMinutes = Math.max(0, totalMins - breakMins);

    const teamName = row.team_name || "Unassigned";
    const employeeKey = `${row.employee_name}_${row.reporting_manager}`;

    if (!teamMap[teamName]) {
      teamMap[teamName] = {};
    }

    if (!teamMap[teamName][employeeKey]) {
      teamMap[teamName][employeeKey] = {
        employeeName: row.employee_name,
        managerName: row.reporting_manager,
        totalMinutes: 0,
      };
    }

    // Ensure we're adding the correct workMinutes value
    teamMap[teamName][employeeKey].totalMinutes += workMinutes;
  });
  
  let summaryHTML = `<h2>Time Log Report (All Offices) - ${currentDate}</h2>`;

    Object.keys(teamMap).forEach((team, idx) => {
    summaryHTML += `
      <h3>${idx + 1}. Department: ${team}</h3>
     <table border="1" style="width: 60%; border-collapse: collapse; margin-bottom: 30px;">
        <tr style="background-color: #f2f2f2;">
          <th style="border: 1px solid #ddd; padding: 8px;">Sr. No.</th>
          <th style="border: 1px solid #ddd; padding: 8px;">Employee Name</th>
          <th style="border: 1px solid #ddd; padding: 8px;">Manager Name</th>
          <th style="border: 1px solid #ddd; padding: 8px;">Total Hours</th>
        </tr>`;

    const teamMembers = Object.values(teamMap[team]);
    teamMembers.forEach((entry, i) => {
      const totalHours = formatMinutesToHhMm(entry.totalMinutes);
      summaryHTML += `
        <tr>
          <td style="border: 1px solid #ddd; padding: 8px;">${i + 1}</td>
          <td style="border: 1px solid #ddd; padding: 8px;">${entry.employeeName}</td>
          <td style="border: 1px solid #ddd; padding: 8px;">${entry.managerName || "-"}</td>
          <td style="border: 1px solid #ddd; padding: 8px;">${totalHours}</td>
        </tr>`;
    });

    summaryHTML += `</table><br>`;
  });


  // No Time Log Table
if (noTimeLogResults.length > 0) {
  summaryHTML += `<h3>Timesheets Not Filled - ${noTimeLogResults.length} Employees</h3>`;
  summaryHTML += ` <table border="1" style="width: 60%; border-collapse: collapse; margin-bottom: 30px;">
    <tr style="background-color: #f2f2f2;">
       <th style="border: 1px solid #ddd; padding: 8px;">Sr. No.</th> <th style="border: 1px solid #ddd; padding: 8px;">Employee Name</th> <th style="border: 1px solid #ddd; padding: 8px;">Department</th> <th style="border: 1px solid #ddd; padding: 8px;">Reporting Manager</th>
    </tr>`;
  noTimeLogResults.forEach((emp, idx) => {
    summaryHTML += `<tr><td style="border: 1px solid #ddd; padding: 8px;">${idx + 1}</td>
    <td style="border: 1px solid #ddd; padding: 8px;">${emp.employee_name}</td>
    <td style="border: 1px solid #ddd; padding: 8px;">${emp.department_name || "No Department Assigned"}</td>
    <td style="border: 1px solid #ddd; padding: 8px;">${emp.reporting_manager ||"-"}</td></tr>`;
  });
  summaryHTML += `</table>`;
} else {
  summaryHTML += `<h3>All employees have logged time today.</h3>`;
}

//Leave Report Table
    if (leaveResults.length > 0) {
    summaryHTML += `<h3>Employees on Leave - ${leaveResults.length}</h3>`;
    summaryHTML += ` <table border="1" style="width: 60%; border-collapse: collapse; margin-bottom: 30px;">
      <tr style="background-color: #f2f2f2;">
         <th style="border: 1px solid #ddd; padding: 8px;">Sr. No.</th> <th style="border: 1px solid #ddd; padding: 8px;">Employee Name</th> <th style="border: 1px solid #ddd; padding: 8px;">Department</th> <th style="border: 1px solid #ddd; padding: 8px;">Reporting Manager</th>
      </tr>`;
    leaveResults.forEach((leave, idx) => {
      summaryHTML += `<tr><td style="border: 1px solid #ddd; padding: 8px;">${idx + 1}</td>
      <td style="border: 1px solid #ddd; padding: 8px;">${leave.employee_name}</td>
      <td style="border: 1px solid #ddd; padding: 8px;">${leave.department_name || "No Department Assigned"}</td>
      <td style="border: 1px solid #ddd; padding: 8px;">${leave.reporting_manager}</td></tr>`;
    });
    summaryHTML += `</table>`;
  } else {
    summaryHTML += `<h3>No employee(s) is on leave today.</h3>`;
  }
  
  return summaryHTML;
}


// CRON Schedule
cron.schedule("15 22 * * *", async () => {
  console.log("Running Daily Time Log Report...");

  const currentDate = new Date().toLocaleDateString("en-GB", {
    timeZone: "Asia/Kolkata",
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });
  try {
    const isHoliday = await isHolidayToday();
    if (isHoliday) {
      console.log("Today is a holiday. Skipping report.");
      return;
    }
    const htmlReport = await generateTimeLogAndLeaveReport(currentDate);
    if (!htmlReport || htmlReport.trim() === "") {
      console.log("No Time Log report generated. Skipping email.");
      return;
    }

    await sendEmail({
      to: `<EMAIL>`,
      cc: `<EMAIL>, <EMAIL>`,
      subject: `Time Log Report (All Offices) - ${currentDate}`,
      html: htmlReport,
    });

    console.log("Time Log Report email sent.");
  } catch (err) {
    console.error("Error while sending time log report:", err.message);
  }
});
