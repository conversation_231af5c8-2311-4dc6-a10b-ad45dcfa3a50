const mysql = require("mysql2");
const nodemailer = require("nodemailer");
const cron = require("node-cron");
const pdf = require("html-pdf");
const ExcelJS = require("exceljs");
const fs = require("fs");
require("dotenv").config();

// Database connection
const db = mysql.createPool({
  connectionLimit: 10,
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  port: process.env.DB_PORT,
  charset: "UTF8_GENERAL_CI",
});

console.log("Database Config:");
console.log("Host:", process.env.DB_HOST);
console.log("User:", process.env.DB_USER);
console.log("Database:", process.env.DB_NAME);
console.log("Port:", process.env.DB_PORT);
console.log("Smtp-User", process.env.SMTP_USER);

// Send email with retries
async function sendEmailWithRetries(
  pdfBuffer,
  summaryPdfBuffer,
  projectSummaryPdfBuffer,
  excelBuffer,
  managerEmail,
  employeeName,
  monthName,
  employeeEmail,
  retries = 30
) {
  const transporter = nodemailer.createTransport({
    service: "gmail",
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASSWORD,
    },
    // Add connection timeout settings
    connectionTimeout: 60000, // 60 seconds
    greetingTimeout: 30000, // 30 seconds
    socketTimeout: 60000, // 60 seconds
    // Use TLS instead of STARTTLS for better compatibility
    secure: true,
    port: 465,
  });

  const currentDate = new Date().toLocaleDateString("en-GB", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });

  const year = new Date().getFullYear();

  const mailOptions = {
    from: `"WFMS" <${process.env.SMTP_USER}>`,
    to: `${managerEmail}, <EMAIL>`,
    cc: `<EMAIL>, <EMAIL>, ${employeeEmail}`,
    subject: `Monthly Sheet - ${monthName} - ${employeeName}`,
    text: `Dear ${employeeName},\n\nPlease find attached your monthly timesheet for the month of ${monthName} ${year}.\n\nThanks and Regards,\n\nWFMS Team`,
    attachments: [
      {
        filename: `Monthly_Sheet_${monthName}_${employeeName}.pdf`,
        content: pdfBuffer,
        contentType: "application/pdf",
      },
      {
        filename: `Daily_Total_Hours_Report_${employeeName}_${monthName}.pdf`,
        content: summaryPdfBuffer,
        contentType: "application/pdf",
      },
      {
        filename: `Project_Hours_Summary_${employeeName}_${monthName}.pdf`,
        content: projectSummaryPdfBuffer,
        contentType: "application/pdf",
      },
      {
        filename: `Monthly_Sheet_${monthName}_${employeeName}.xlsx`,
        content: excelBuffer,
        contentType:
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      },
    ],
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log("(Manager)  -->> Message sent to: %s", managerEmail);
    console.log("(Employee) -->> Message sent to: %s", employeeEmail);
  } catch (error) {
    if (retries > 0 && [421, 450, 451, 454].includes(error.responseCode)) {
      console.log(`Retrying to send email. Attempts left: ${retries - 1}`);
      setTimeout(() => {
        sendEmailWithRetries(
          pdfBuffer,
          summaryPdfBuffer,
          projectSummaryPdfBuffer,
          excelBuffer,
          managerEmail,
          employeeName,
          monthName,
          employeeEmail,
          retries - 1
        );
      }, 60000); // Retry after 1 minute
    } else {
      console.error("Failed to send email:", error);
    }
  }
}

async function sendNotificationEmail() {
  const transporter = nodemailer.createTransport({
    service: "gmail",
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASSWORD,
    },
    // Add connection timeout settings
    connectionTimeout: 60000, // 60 seconds
    greetingTimeout: 30000, // 30 seconds
    socketTimeout: 60000, // 60 seconds
    // Use TLS instead of STARTTLS for better compatibility
    secure: true,
    port: 465,
  });

  const currentDate = new Date().toLocaleDateString("en-GB", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });

  const mailOptions = {
    from: `"WFMS" <${process.env.SMTP_USER}>`,
    to: `<EMAIL>, <EMAIL>`,
    cc: `<EMAIL>`,
    subject: `Monthly Email Sending Status - ${currentDate}`,
    html: `Dear Sir,<br><br>All Monthly sheets mails were sent successfully on ${currentDate}.<br><br>Thanks and Regards,<br><br>WFMS Team`,
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log("Notification email sent.");
  } catch (error) {
    console.error("Failed to send notification email:", error);
    // Log additional details for debugging
    console.error("Error code:", error.code);
    console.error("Error message:", error.message);
    if (error.response) {
      console.error("SMTP response:", error.response);
    }
  }
}

// Utility functions
function formatDate(dateTimeStr) {
  if (!dateTimeStr) return "-";
  const dateTime = new Date(dateTimeStr);
  if (isNaN(dateTime.getTime())) return "-";
  const day = ("0" + dateTime.getDate()).slice(-2);
  const month = ("0" + (dateTime.getMonth() + 1)).slice(-2);
  const year = dateTime.getFullYear();
  return `${day}-${month}-${year}`;
}

function convertMinutesToHHMM(minutes) {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  const formattedHours = hours < 10 ? `0${hours}` : hours;
  const formattedMinutes = mins < 10 ? `0${mins}` : mins;
  return `${formattedHours}:${formattedMinutes}`;
}

function getMonthName(monthNumber) {
  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];
  return months[monthNumber];
}

// Database query function
function queryDb(query) {
  return new Promise((resolve, reject) => {
    db.query(query, (err, results) => {
      if (err) return reject(err);
      resolve(results);
    });
  });
}

// Get monthly report content
async function getMonthlyReportContent() {
  const currentDate = new Date();
  // const firstDayOfMonth = "2025-06-01"
  // const lastDayOfMonth = "2025-07-01"
  const firstDayOfMonth = new Date(
    currentDate.getFullYear(),
    currentDate.getMonth(),
    2
  )
    .toISOString()
    .slice(0, 10);
  const lastDayOfMonth = new Date(
    currentDate.getFullYear(),
    currentDate.getMonth() + 1,
    2
  )
    .toISOString()
    .slice(0, 10);

  const reportQuery = `
    SELECT 
        e.name AS employee_name,
        e.email AS employee_email,
        u.email AS manager_email,
        pt.memo,
        pt.total_minutes,
        pt.start_time,
        pt.end_time,
        t.heading AS task_heading,
        p.project_name,
        COALESCE(SUM(b.total_minutes), 0) AS total_break_minutes
    FROM 
        project_time_logs pt
    JOIN 
        users e ON pt.user_id = e.id
    LEFT JOIN 
        tasks t ON pt.task_id = t.id
    LEFT JOIN 
        project_time_log_breaks b ON pt.id = b.project_time_log_id
    LEFT JOIN 
        projects p ON pt.project_id = p.id
    LEFT JOIN 
        employee_details ed ON e.id = ed.user_id
    LEFT JOIN 
        users u ON ed.reporting_to = u.id
    WHERE 
        pt.start_time >= ? AND pt.start_time <= ?
    GROUP BY
        pt.id, e.name, e.email, u.email, pt.memo, pt.total_minutes, pt.start_time, pt.end_time, t.heading, p.project_name
    ORDER BY 
        e.email, pt.start_time;
  `;

  try {
    const results = await queryDb(
      mysql.format(reportQuery, [firstDayOfMonth, lastDayOfMonth])
    );
    return generateMonthlyReport(results);
  } catch (err) {
    console.error(err);
  }
}

// Generate monthly report content, create PDF and Excel
async function generateMonthlyReport(results) {
  let reports = {};
  const currentDate = new Date().toLocaleDateString("en-GB", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });
  const monthName = getMonthName(new Date().getMonth());
  const year = new Date().getFullYear();

  // Read the logo file
  const logoPath = "rkdlogo.png";
  const logoData = fs.readFileSync(logoPath).toString("base64");

  results.forEach((row) => {
    const key = row.employee_email;
    if (!reports[key]) {
      reports[key] = {
        employeeName: row.employee_name,
        employeeEmail: row.employee_email,
        managerEmail: row.manager_email,
        reportContent: "",
        summaryContent: "",
        projectSummaryContent: "",
        dailyHours: {},
      };
    }

    const taskDate = formatDate(row.start_time);
    const workMinutes =
      parseInt(row.total_minutes, 10) - parseInt(row.total_break_minutes, 10);
    if (!reports[key].dailyHours[taskDate]) {
      reports[key].dailyHours[taskDate] = 0;
    }
    reports[key].dailyHours[taskDate] += workMinutes;
  });

  for (const key in reports) {
    let reportContent = `
      <style>
        @page {
          margin: 10mm;
        }
        table {
          width: 100%;
          border-collapse: collapse;
        }
        th, td {
          border: 1px solid #000;
          padding: 8px;
          text-align: center; 
        }
        th {
          background-color: #f2f2f2;
        }
        tr {
          page-break-inside: avoid;
        }
        .header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
        }
        .logo {
          width: 100px;
          height: auto;
        }
        .report-title {
          margin-top: 10px;
          margin-bottom: 10px;
        }
      </style>
      <div class="header">
        <img src="data:image/png;base64,${logoData}" class="logo" alt="Company Logo">
      </div>
      <h3 class="report-title">${reports[key].employeeName} - ${monthName} - ${year}</h3>
      <table>
        <tr>
          <th>Sr. No.</th>
          <th>Date</th>
          <th>Total Hours</th>
          <th>Project</th>
          <th>Task</th>
          <th>Total Day Hours</th>
        </tr>`;

    let srNo = 1;
    let lastDate = null;

    results
      .filter((row) => row.employee_email === key)
      .forEach((row, index) => {
        const taskDate = formatDate(row.start_time);
        const taskTotalHoursAndMinutes = convertMinutesToHHMM(
          parseInt(row.total_minutes, 10)
        );
        const project = row.project_name || "-";
        const task = row.task_heading || "-";
        const totalDayHours = convertMinutesToHHMM(
          reports[key].dailyHours[taskDate]
        );

        reportContent += `
          <tr>
            <td>${taskDate !== lastDate ? srNo++ : ""}</td>
            <td>${taskDate !== lastDate ? taskDate : ""}</td>
            <td>${taskTotalHoursAndMinutes}</td>
            <td style="text-align: left;">${project}</td>
            <td style="text-align: left;">${task}</td>
            <td>${taskDate !== lastDate ? totalDayHours : ""}</td>
          </tr>`;

        lastDate = taskDate;
      });

    // Set up content for the summary page
    let summaryContent = `
      <style>
        @page {
          margin: 10mm;
        }
        table {
          width: 100%;
          border-collapse: collapse;
        }
        th, td {
          border: 1px solid #000;
          padding: 8px;
          text-align: center; 
        }
        th {
          background-color: #f2f2f2;
        }
        tr {
          page-break-inside: avoid;
        }
        .header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
        }
        .logo {
          width: 100px;
          height: auto;
        }
        .report-title {
          margin-top: 10px;
          margin-bottom: 10px;
        }
      </style>
      <div class="header">
        <img src="data:image/png;base64,${logoData}" class="logo" alt="Company Logo">
      </div>
      <h3 class="report-title">Daily Total Hours Report - ${reports[key].employeeName}</h3>
      <table>
        <tr>
          <th>Sr. No.</th>
          <th>Date</th>
          <th>Total Day Hours</th>
        </tr>`;

    let summarySrNo = 1;
    for (const [date, hours] of Object.entries(reports[key].dailyHours)) {
      const totalDayHours = convertMinutesToHHMM(hours);
      summaryContent += `
        <tr>
          <td>${summarySrNo++}</td>
          <td>${date}</td>
          <td>${totalDayHours}</td>
        </tr>`;
    }

    summaryContent += `
      </table>
      <br><br>`;

    // Set up content for the project summary page
    let projectSummaryContent = `
      <style>
        @page {
          margin: 10mm;
        }
        table {
          width: 100%;
          border-collapse: collapse;
        }
        th, td {
          border: 1px solid #000;
          padding: 8px;
          text-align: center; 
        }
        th {
          background-color: #f2f2f2;
        }
        tr {
          page-break-inside: avoid;
        }
        .header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
        }
        .logo {
          width: 100px;
          height: auto;
        }
        .report-title {
          margin-top: 10px;
          margin-bottom: 10px;
        }
      </style>
      <div class="header">
        <img src="data:image/png;base64,${logoData}" class="logo" alt="Company Logo">
      </div>
      <h3 class="report-title">Project Hours Summary - ${reports[key].employeeName}</h3>
      <table>
        <tr>
          <th>Sr. No.</th>
          <th>Project</th>
          <th>Total Hours</th>
        </tr>`;

    let projectSummarySrNo = 1;
    const projectHours = {};
    results
      .filter((row) => row.employee_email === key)
      .forEach((row) => {
        const project =
          row.project_name || "Time filled in task without project";
        const workMinutes =
          parseInt(row.total_minutes, 10) -
          parseInt(row.total_break_minutes, 10);
        if (!projectHours[project]) {
          projectHours[project] = 0;
        }
        projectHours[project] += workMinutes;
      });

    // Sort projects and ensure "Ideal time without project" comes last
    const sortedProjects = Object.entries(projectHours).sort((a, b) => {
      if (a[0] === "Time filled in task without project") return 1;
      if (b[0] === "Time filled in task without project") return -1;
      return a[0].localeCompare(b[0]);
    });

    for (const [project, hours] of sortedProjects) {
      const totalHours = convertMinutesToHHMM(hours);
      projectSummaryContent += `
        <tr>
          <td>${projectSummarySrNo++}</td>
          <td style="text-align: left;">${project}</td>
          <td>${totalHours}</td>
        </tr>`;
    }

    projectSummaryContent += `
      </table>
      <br><br>`;

    reports[key].reportContent = reportContent;
    reports[key].summaryContent = summaryContent;
    reports[key].projectSummaryContent = projectSummaryContent;

    // Generate PDFs
    const pdfOptions = {
      format: "A4",
      border: "10mm",
    };

    const reportPdfBuffer = await new Promise((resolve, reject) => {
      pdf
        .create(reports[key].reportContent, pdfOptions)
        .toBuffer((err, buffer) => {
          if (err) reject(err);
          else resolve(buffer);
        });
    });

    const summaryPdfBuffer = await new Promise((resolve, reject) => {
      pdf
        .create(reports[key].summaryContent, pdfOptions)
        .toBuffer((err, buffer) => {
          if (err) reject(err);
          else resolve(buffer);
        });
    });

    const projectSummaryPdfBuffer = await new Promise((resolve, reject) => {
      pdf
        .create(reports[key].projectSummaryContent, pdfOptions)
        .toBuffer((err, buffer) => {
          if (err) reject(err);
          else resolve(buffer);
        });
    });

    // Generate Excel file as buffer
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Monthly Report");
    worksheet.columns = [
      { header: "Sr. No.", key: "srNo", width: 10 },
      { header: "Date", key: "date", width: 15 },
      { header: "Total Hours", key: "totalHours", width: 15 },
      { header: "Project", key: "project", width: 20 },
      { header: "Task", key: "task", width: 20 },
      { header: "Total Day Hours", key: "totalDayHours", width: 15 },
    ];

    let excelSrNo = 1;
    results
      .filter((row) => row.employee_email === key)
      .forEach((row, index) => {
        const taskDate = formatDate(row.start_time);
        const taskTotalHoursAndMinutes = convertMinutesToHHMM(
          parseInt(row.total_minutes, 10)
        );
        const project = row.project_name || "-";
        const task = row.task_heading || "-";
        const totalDayHours = convertMinutesToHHMM(
          reports[key].dailyHours[taskDate]
        );

        worksheet.addRow({
          srNo: taskDate !== lastDate ? excelSrNo++ : "",
          date: taskDate !== lastDate ? taskDate : "",
          totalHours: taskTotalHoursAndMinutes,
          project: project,
          task: task,
          totalDayHours: taskDate !== lastDate ? totalDayHours : "",
        });

        lastDate = taskDate;
      });

    const excelBuffer = await workbook.xlsx.writeBuffer();

    if (
      reportPdfBuffer &&
      summaryPdfBuffer &&
      projectSummaryPdfBuffer &&
      excelBuffer
    ) {
      // Send detailed report and summary in separate emails
      await sendEmailWithRetries(
        reportPdfBuffer,
        summaryPdfBuffer,
        projectSummaryPdfBuffer,
        excelBuffer,
        reports[key].managerEmail,
        reports[key].employeeName,
        monthName,
        reports[key].employeeEmail
      );
    } else {
      console.error(
        "Failed to generate PDF or Excel for employee:",
        reports[key].employeeEmail
      );
    }
  }

  return reports;
}

cron.schedule("00 23 31 * *", async () => {
  try {
    console.log("Starting monthly report generation...");
    await getMonthlyReportContent();
    console.log("All reports generated and emails sent.");

    // Send notification email after all reports are generated and emails are sent
    await sendNotificationEmail();
  } catch (err) {
    console.error("Error generating reports or sending emails:", err);
  }
});

console.log(
  "Cron job scheduled to run on the last day of every month at 11:00 PM"
);

// Add SMTP connection test function
async function testSMTPConnection() {
  const transporter = nodemailer.createTransport({
    service: "gmail",
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASSWORD,
    },
    connectionTimeout: 60000,
    greetingTimeout: 30000,
    socketTimeout: 60000,
    secure: true,
    port: 465,
  });

  try {
    await transporter.verify();
    console.log("SMTP connection test successful!");
    return true;
  } catch (error) {
    console.error("SMTP connection test failed:", error);
    return false;
  }
}

// Test SMTP connection on startup
console.log("Testing SMTP connection...");
testSMTPConnection().then((isConnected) => {
  if (isConnected) {
    console.log("SMTP connection is working properly.");
  } else {
    console.log("SMTP connection failed. Please check your configuration.");
  }
});
