/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace translate_v2 {
    export interface Options extends GlobalOptions {
        version: 'v2';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * OAuth bearer token.
         */
        bearer_token?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Pretty-print response.
         */
        pp?: boolean;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters. Overrides userIp if both are provided.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Google Cloud Translation API
     *
     * The Google Cloud Translation API lets websites and programs integrate with
     *     Google Translate programmatically.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const translate = google.translate('v2');
     * ```
     */
    export class Translate {
        context: APIRequestContext;
        detections: Resource$Detections;
        languages: Resource$Languages;
        translations: Resource$Translations;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    export interface Schema$DetectionsListResponse {
        /**
         * A detections contains detection results of several text
         */
        detections?: Schema$DetectionsResource[];
    }
    /**
     * An array of languages which we detect for the given text The most likely language list first.
     */
    export interface Schema$DetectionsResource {
    }
    /**
     * The request message for language detection.
     */
    export interface Schema$DetectLanguageRequest {
        /**
         * The input text upon which to perform language detection. Repeat this
         * parameter to perform language detection on multiple text inputs.
         */
        q?: string[] | null;
    }
    /**
     * The request message for discovering supported languages.
     */
    export interface Schema$GetSupportedLanguagesRequest {
        /**
         * The language to use to return localized, human readable names of supported
         * languages.
         */
        target?: string | null;
    }
    export interface Schema$LanguagesListResponse {
        /**
         * List of source/target languages supported by the translation API. If target parameter is unspecified, the list is sorted by the ASCII code point order of the language code. If target parameter is specified, the list is sorted by the collation order of the language name in the target language.
         */
        languages?: Schema$LanguagesResource[];
    }
    export interface Schema$LanguagesResource {
        /**
         * Supported language code, generally consisting of its ISO 639-1
         * identifier. (E.g. 'en', 'ja'). In certain cases, BCP-47 codes including
         * language + region identifiers are returned (e.g. 'zh-TW' and 'zh-CH')
         */
        language?: string | null;
        /**
         * Human readable name of the language localized to the target language.
         */
        name?: string | null;
    }
    /**
     * The main translation request message for the Cloud Translation API.
     */
    export interface Schema$TranslateTextRequest {
        /**
         * The format of the source text, in either HTML (default) or plain-text. A
         * value of "html" indicates HTML and a value of "text" indicates plain-text.
         */
        format?: string | null;
        /**
         * The `model` type requested for this translation. Valid values are
         * listed in public documentation.
         */
        model?: string | null;
        /**
         * The input text to translate. Repeat this parameter to perform translation
         * operations on multiple text inputs.
         */
        q?: string[] | null;
        /**
         * The language of the source text, set to one of the language codes listed in
         * Language Support. If the source language is not specified, the API will
         * attempt to identify the source language automatically and return it within
         * the response.
         */
        source?: string | null;
        /**
         * The language to use for translation of the input text, set to one of the
         * language codes listed in Language Support.
         */
        target?: string | null;
    }
    /**
     * The main language translation response message.
     */
    export interface Schema$TranslationsListResponse {
        /**
         * Translations contains list of translation results of given text
         */
        translations?: Schema$TranslationsResource[];
    }
    export interface Schema$TranslationsResource {
        /**
         * The source language of the initial request, detected automatically, if
         * no source language was passed within the initial request. If the
         * source language was passed, auto-detection of the language will not
         * occur and this field will be empty.
         */
        detectedSourceLanguage?: string | null;
        /**
         * The `model` type used for this translation. Valid values are
         * listed in public documentation. Can be different from requested `model`.
         * Present only if specific model type was explicitly requested.
         */
        model?: string | null;
        /**
         * Text translated into the target language.
         */
        translatedText?: string | null;
    }
    export class Resource$Detections {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Detects the language of text within a request.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        detect(params: Params$Resource$Detections$Detect, options: StreamMethodOptions): GaxiosPromise<Readable>;
        detect(params?: Params$Resource$Detections$Detect, options?: MethodOptions): GaxiosPromise<Schema$DetectionsListResponse>;
        detect(params: Params$Resource$Detections$Detect, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        detect(params: Params$Resource$Detections$Detect, options: MethodOptions | BodyResponseCallback<Schema$DetectionsListResponse>, callback: BodyResponseCallback<Schema$DetectionsListResponse>): void;
        detect(params: Params$Resource$Detections$Detect, callback: BodyResponseCallback<Schema$DetectionsListResponse>): void;
        detect(callback: BodyResponseCallback<Schema$DetectionsListResponse>): void;
        /**
         * Detects the language of text within a request.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Detections$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Detections$List, options?: MethodOptions): GaxiosPromise<Schema$DetectionsListResponse>;
        list(params: Params$Resource$Detections$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Detections$List, options: MethodOptions | BodyResponseCallback<Schema$DetectionsListResponse>, callback: BodyResponseCallback<Schema$DetectionsListResponse>): void;
        list(params: Params$Resource$Detections$List, callback: BodyResponseCallback<Schema$DetectionsListResponse>): void;
        list(callback: BodyResponseCallback<Schema$DetectionsListResponse>): void;
    }
    export interface Params$Resource$Detections$Detect extends StandardParameters {
        /**
         * Request body metadata
         */
        requestBody?: Schema$DetectLanguageRequest;
    }
    export interface Params$Resource$Detections$List extends StandardParameters {
        /**
         * The input text upon which to perform language detection. Repeat this
         * parameter to perform language detection on multiple text inputs.
         */
        q?: string[];
    }
    export class Resource$Languages {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Returns a list of supported languages for translation.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Languages$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Languages$List, options?: MethodOptions): GaxiosPromise<Schema$LanguagesListResponse>;
        list(params: Params$Resource$Languages$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Languages$List, options: MethodOptions | BodyResponseCallback<Schema$LanguagesListResponse>, callback: BodyResponseCallback<Schema$LanguagesListResponse>): void;
        list(params: Params$Resource$Languages$List, callback: BodyResponseCallback<Schema$LanguagesListResponse>): void;
        list(callback: BodyResponseCallback<Schema$LanguagesListResponse>): void;
    }
    export interface Params$Resource$Languages$List extends StandardParameters {
        /**
         * The model type for which supported languages should be returned.
         */
        model?: string;
        /**
         * The language to use to return localized, human readable names of supported
         * languages.
         */
        target?: string;
    }
    export class Resource$Translations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Translates input text, returning translated text.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Translations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Translations$List, options?: MethodOptions): GaxiosPromise<Schema$TranslationsListResponse>;
        list(params: Params$Resource$Translations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Translations$List, options: MethodOptions | BodyResponseCallback<Schema$TranslationsListResponse>, callback: BodyResponseCallback<Schema$TranslationsListResponse>): void;
        list(params: Params$Resource$Translations$List, callback: BodyResponseCallback<Schema$TranslationsListResponse>): void;
        list(callback: BodyResponseCallback<Schema$TranslationsListResponse>): void;
        /**
         * Translates input text, returning translated text.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        translate(params: Params$Resource$Translations$Translate, options: StreamMethodOptions): GaxiosPromise<Readable>;
        translate(params?: Params$Resource$Translations$Translate, options?: MethodOptions): GaxiosPromise<Schema$TranslationsListResponse>;
        translate(params: Params$Resource$Translations$Translate, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        translate(params: Params$Resource$Translations$Translate, options: MethodOptions | BodyResponseCallback<Schema$TranslationsListResponse>, callback: BodyResponseCallback<Schema$TranslationsListResponse>): void;
        translate(params: Params$Resource$Translations$Translate, callback: BodyResponseCallback<Schema$TranslationsListResponse>): void;
        translate(callback: BodyResponseCallback<Schema$TranslationsListResponse>): void;
    }
    export interface Params$Resource$Translations$List extends StandardParameters {
        /**
         * The customization id for translate
         */
        cid?: string[];
        /**
         * The format of the source text, in either HTML (default) or plain-text. A
         * value of "html" indicates HTML and a value of "text" indicates plain-text.
         */
        format?: string;
        /**
         * The `model` type requested for this translation. Valid values are
         * listed in public documentation.
         */
        model?: string;
        /**
         * The input text to translate. Repeat this parameter to perform translation
         * operations on multiple text inputs.
         */
        q?: string[];
        /**
         * The language of the source text, set to one of the language codes listed in
         * Language Support. If the source language is not specified, the API will
         * attempt to identify the source language automatically and return it within
         * the response.
         */
        source?: string;
        /**
         * The language to use for translation of the input text, set to one of the
         * language codes listed in Language Support.
         */
        target?: string;
    }
    export interface Params$Resource$Translations$Translate extends StandardParameters {
        /**
         * Request body metadata
         */
        requestBody?: Schema$TranslateTextRequest;
    }
    export {};
}
