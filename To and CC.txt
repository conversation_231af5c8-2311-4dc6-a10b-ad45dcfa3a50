///For daily mail sheet
to: managerEmail,
cc: `<EMAIL>, ${employeeEmail}`,

//for notification mail
to: `<EMAIL>, <EMAIL>`,
cc: `<EMAIL>`,

///For monthly mail sheet
to: `${managerEmail}, <EMAIL>`,
cc: `<EMAIL>, <EMAIL>, ${employeeEmail}`,

//for notification mail
to: `<EMAIL>, <EMAIL>`,
cc: `<EMAIL>`,




// in query
        a.clock_in_time,
        a.clock_out_time

        LEFT JOIN
        attendances a ON e.id = a.user_id AND DATE(a.clock_in_time) = CURDATE()



        //<h4>1. Clock In and Clock Out</h4>
          // <table border="1" style="width: 100%; border-collapse: collapse;">
          //   <tr style="background-color: #f2f2f2;">
          //     <th style="border: 1px solid #ddd; padding: 8px;">Clock In Time</th>
          //     <th style="border: 1px solid #ddd; padding: 8px;">Clock Out Time</th>
          //     <th style="border: 1px solid #ddd; padding: 8px;">Total Hours (Clock In to Clock Out)</th>
          //   </tr>
          //   <tr>
          //     <td style="border: 1px solid #ddd; text-align: center; font-weight: bold; padding: 8px;">${formatTimeTo12Hour(reports[key].clockInTime)}</td>
          //     <td style="border: 1px solid #ddd; text-align: center; font-weight: bold; padding: 8px;">${reports[key].clockOutTime ? formatTimeTo12Hour(reports[key].clockOutTime) : "Didn't clock out"}</td>
          //     <td style="border: 1px solid #ddd; text-align: center; font-weight: bold; padding: 8px;">${totalClockInHours}</td>
          //   </tr>
          // </table>
          // <br>