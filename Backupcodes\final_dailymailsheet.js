const mysql = require("mysql2");
const nodemailer = require("nodemailer");
const cron = require("node-cron");
const fs = require("fs");
const path = require("path");
require("dotenv").config();

// Create a pool of database connections
const db = mysql.createPool({
  connectionLimit: 10,
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  port: process.env.DB_PORT,
  charset: "UTF8_GENERAL_CI",
});

console.log("Database Config:");
console.log("Host:", process.env.DB_HOST);
console.log("User:", process.env.DB_USER);
console.log("Database:", process.env.DB_NAME);
console.log("Port:", process.env.DB_PORT);
console.log("Smtp-User", process.env.SMTP_USER);

// Path to the log file
const logFilePath = path.join(__dirname, "email_log.json");

// Read log file
function readLogFile() {
  if (!fs.existsSync(logFilePath)) return { sent: [], unsent: [] };
  const data = fs.readFileSync(logFilePath);
  return JSON.parse(data);
}

// Write log file
function writeLogFile(logs) {
  fs.writeFileSync(logFilePath, JSON.stringify(logs, null, 2));
}

// Add an entry to the log file
function logEmailStatus(status, managerEmail, employeeEmail) {
  const logs = readLogFile();

  if (status === "sent") {
    logs.sent.push({ managerEmail, employeeEmail });
  } else {
    logs.unsent.push({ managerEmail, employeeEmail });
  }

  writeLogFile(logs);
}

// Check if an email is already logged
function isEmailLogged(status, managerEmail, employeeEmail) {
  const logs = readLogFile();
  return logs[status].some(
    (log) =>
      log.managerEmail === managerEmail && log.employeeEmail === employeeEmail
  );
}

async function sendEmailWithRetries(
  reportContent,
  managerEmail,
  employeeName,
  employeeEmail,
  retries = 50
) {
  const transporter = nodemailer.createTransport({
    host: "smtp.gmail.com",
    port: 587,
    secure: false,
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASSWORD,
    },
  });

  const currentDate = new Date().toLocaleDateString("en-GB", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });

  const mailOptions = {
    from: `"WFMS" <${process.env.SMTP_USER}>`,
    // to: "<EMAIL>",
    to: managerEmail,
    cc: `<EMAIL>, ${employeeEmail}`,
    subject: `Daily Sheet - ${currentDate} - ${employeeName}`,
    html: reportContent,
  };

  for (let attempt = 0; attempt < retries; attempt++) {
    try {
      await transporter.sendMail(mailOptions);
      console.log("(Manager)  -->> Message sent to: %s", managerEmail);
      console.log("(Employee) -->> Message sent to: %s", employeeEmail);
      logEmailStatus("sent", managerEmail, employeeEmail);
      return; // Resolve the promise after a successful send
    } catch (error) {
      if (
        attempt < retries - 1 &&
        [421, 450, 451, 454].includes(error.responseCode)
      ) {
        console.log(
          `Retrying to send email. Attempts left: ${retries - attempt - 1}`
        );
        await new Promise((resolve) => setTimeout(resolve, 60000));
      } else {
        console.error("Failed to send email:", error);
        logEmailStatus("unsent", managerEmail, employeeEmail);
        break; // Exit the loop after max retries or non-retriable error
      }
    }
  }
}

function formatDate(dateTimeStr) {
  if (!dateTimeStr) return "-";
  const dateTime = new Date(dateTimeStr);
  if (isNaN(dateTime.getTime())) return "-";
  const day = ("0" + dateTime.getDate()).slice(-2);
  const month = ("0" + (dateTime.getMonth() + 1)).slice(-2);
  const year = dateTime.getFullYear();
  // const hours = ("0" + dateTime.getHours()).slice(-2);
  // const minutes = ("0" + dateTime.getMinutes()).slice(-2);
  // const seconds = ("0" + dateTime.getSeconds()).slice(-2);
  return `${day}/${month}/${year}`;
}

// ${hours}:${minutes}:${seconds}

function formatTimeTo12Hour(dateTimeStr) {
  if (!dateTimeStr) return "-";
  const dateTime = new Date(dateTimeStr);
  if (isNaN(dateTime.getTime())) return "-";

  // Convert to local time if needed (assuming the server and database are using UTC)
  const localDateTime = new Date(dateTime.getTime() + 330 * 60 * 1000); // 330 minutes for IST offset

  let hours = localDateTime.getHours();
  const minutes = ("0" + localDateTime.getMinutes()).slice(-2);
  const ampm = hours >= 12 ? "PM" : "AM";
  hours = hours % 12;
  hours = hours ? hours : 12; // the hour '0' should be '12'
  return `${hours}:${minutes} ${ampm}`;
}

function convertMinutesToHHMM(minutes) {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours.toString().padStart(2, "0")}:${mins
    .toString()
    .padStart(2, "0")}`;
}

function calculateTotalHoursBetween(clockIn, clockOut) {
  if (!clockIn || !clockOut) return "-";

  const clockInTime = new Date(clockIn);
  const clockOutTime = new Date(clockOut);

  const diffInMs = clockOutTime - clockInTime;
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInMinutes = Math.floor((diffInMs % (1000 * 60 * 60)) / (1000 * 60));

  return `${diffInHours}h ${diffInMinutes}m`;
}

async function getDailyReportContent() {
  const query = `
    SELECT 
        u.name AS reporting_manager,
        u.email AS manager_email,
        e.name AS employee_name,
        e.email AS employee_email,
        pt.memo,
        pt.total_minutes,
        pt.start_time,
        pt.end_time,
        p.project_name,
        p.id AS project_id,
        t.heading AS task_heading,
        t.id AS task_id,
        t.start_date AS task_start_time,
        t.due_date AS task_due_date,
        c.name AS client_name,
        COALESCE(SUM(b.total_minutes), 0) AS total_break_minutes,
        (
          SELECT 
              ROUND(SUM(CAST(pt2.total_minutes AS UNSIGNED)) / 60, 2)
          FROM 
              project_time_logs pt2
          WHERE 
              pt2.project_id = pt.project_id
        ) AS total_project_hours,
        (
          SELECT 
              ROUND(SUM(CAST(pt3.total_minutes AS UNSIGNED)) / 60, 2)
          FROM 
              project_time_logs pt3
          WHERE 
              pt3.project_id = pt.project_id AND pt3.user_id = e.id
        ) AS user_project_hours,
        (
          SELECT 
              ROUND((SUM(CAST(pt4.total_minutes AS UNSIGNED)) - 
              IFNULL((SELECT SUM(CAST(b2.total_minutes AS UNSIGNED)) 
                     FROM project_time_log_breaks b2 
                     WHERE b2.project_time_log_id IN 
                           (SELECT pt5.id FROM project_time_logs pt5 
                            WHERE pt5.task_id = pt.task_id AND pt5.user_id = e.id)
                    ), 0)) / 60, 2)
          FROM 
              project_time_logs pt4
          WHERE 
              pt4.task_id = pt.task_id AND pt4.user_id = e.id
        ) AS user_task_hours
    FROM 
        project_time_logs pt
    JOIN 
        users e ON pt.user_id = e.id AND e.status = 'active'
    JOIN 
        employee_details ed ON e.id = ed.user_id
    JOIN 
        users u ON ed.reporting_to = u.id
    LEFT JOIN 
        projects p ON pt.project_id = p.id
    LEFT JOIN 
        users c ON p.client_id = c.id
    LEFT JOIN 
        tasks t ON pt.task_id = t.id
    LEFT JOIN 
        project_time_log_breaks b ON pt.id = b.project_time_log_id
    WHERE 
        DATE(pt.start_time) = CURDATE()
    GROUP BY
        pt.id, u.name, u.email, e.name, e.email, pt.memo, pt.total_minutes, pt.start_time, pt.end_time, p.project_name, p.id, t.heading, t.id, t.start_date, t.due_date, c.name
    ORDER BY 
        u.email, e.name;
  `;

  const pendingProjectsQuery = `
    SELECT
        p.id AS project_id,
        p.project_name,
        p.status,
        u.name AS client_name,
        p.start_date,
        p.deadline AS due_date,
        pm.user_id AS project_member_id,
        e.name AS project_member_name,
        (
          SELECT 
              ROUND(SUM(CAST(pt.total_minutes AS UNSIGNED)) / 60, 2)
          FROM 
              project_time_logs pt
          WHERE 
              pt.project_id = p.id
        ) AS total_project_hours,
        (
          SELECT 
              ROUND(SUM(CAST(pt2.total_minutes AS UNSIGNED)) / 60, 2)
          FROM 
              project_time_logs pt2
          WHERE 
              pt2.project_id = p.id AND pt2.user_id = pm.user_id
        ) AS user_project_hours
    FROM
        projects p
    LEFT JOIN
        users u ON p.client_id = u.id
    LEFT JOIN
        project_members pm ON p.id = pm.project_id
    LEFT JOIN
        users e ON pm.user_id = e.id AND e.status = 'active'
    WHERE
        p.status IN ('In Progress', 'Sent to Client', 'Not Started')
    ORDER BY
        p.project_name, e.name;
  `;

  try {
    const [results, pendingResults] = await Promise.all([
      queryDb(query),
      queryDb(pendingProjectsQuery),
    ]);
    return generateReport(results, pendingResults);
  } catch (err) {
    console.error(err);
    throw err;
  }
}

function queryDb(query) {
  return new Promise((resolve, reject) => {
    db.query(query, (err, results) => {
      if (err) return reject(err);
      resolve(results);
    });
  });
}

function generateReport(results, pendingResults) {
  let reports = {};
  const currentDate = new Date().toLocaleDateString("en-GB", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });

  if (results.length > 0) {
    results.forEach((row) => {
      if (!row.end_time) return;
      const key = `${row.manager_email}_${row.employee_name}`;
      if (!reports[key]) {
        reports[key] = {
          managerEmail: row.manager_email,
          employeeName: row.employee_name,
          employeeEmail: row.employee_email,
          totalMinutes: 0,
          reportContent: "",
          clockInTime: row.clock_in_time,
          clockOutTime: row.clock_out_time,
          pendingProjects: {
            "In Progress": {},
            "Sent to Client": {},
            "Not Started": {},
          },
        };
      }
      const workMinutes =
        row.total_minutes >= row.total_break_minutes
          ? parseInt(row.total_minutes, 10) -
            parseInt(row.total_break_minutes, 10)
          : parseInt(row.total_break_minutes, 10) -
            parseInt(row.total_minutes, 10);
      reports[key].totalMinutes += workMinutes;
    });

    pendingResults.forEach((row) => {
      Object.keys(reports).forEach((employeeKey) => {
        const employeeName = reports[employeeKey].employeeName;
        const projectMembers = pendingResults
          .filter(
            (member) =>
              member.project_id === row.project_id &&
              member.project_member_name !== employeeName &&
              member.project_member_name
          )
          .map((member) => member.project_member_name)
          .filter(Boolean)
          .join(", ");

        if (
          row.project_member_name === employeeName ||
          row.added_by === row.project_member_id
        ) {
          if (!reports[employeeKey].pendingProjects[row.status]) {
            reports[employeeKey].pendingProjects[row.status] = {};
          }
          if (
            !reports[employeeKey].pendingProjects[row.status][row.project_id]
          ) {
            reports[employeeKey].pendingProjects[row.status][row.project_id] = {
              project_name: row.project_name,
              status: row.status,
              client_name: row.client_name,
              start_date: formatDate(row.start_date),
              due_date: formatDate(row.due_date),
              members: projectMembers || "-",
              total_project_hours: row.total_project_hours || "0.00",
              user_project_hours: row.user_project_hours || "0.00",
            };
          }
        }
      });
    });

    for (const key in reports) {
      const totalHoursAndMinutes = convertMinutesToHHMM(
        reports[key].totalMinutes
      );

      // Update the first table headers to add Total Task Hours column after Task
      reports[key].reportContent = `
          <h3>${reports[key].employeeName} - ${currentDate} - Total hours - ${totalHoursAndMinutes}</h3>
          <h4>1. Work done on ${currentDate}</h4>
          <table border="1" style="width: 100%; border-collapse: collapse;">
            <tr style="background-color: #f2f2f2;">
              <th style="border: 1px solid #ddd; padding: 8px;">Sr. No.</th>
              <th style="border: 1px solid #ddd; padding: 8px;">Project</th>
              <th style="border: 1px solid #ddd; padding: 8px;">Task</th>
              <th style="border: 1px solid #ddd; padding: 8px;">Total Task Hours</th>
              <th style="border: 1px solid #ddd; padding: 8px;">Client</th>
              <th style="border: 1px solid #ddd; padding: 8px;">Memo</th>
              <th style="border: 1px solid #ddd; padding: 8px;">Start Time</th>
              <th style="border: 1px solid #ddd; padding: 8px;">End Time</th>
              <th style="border: 1px solid #ddd; padding: 8px;">Total Hours</th>
            </tr>`;

      // Update the first table rows to include the Total Task Hours column
      results
        .filter(
          (row) =>
            `${row.manager_email}_${row.employee_name}` === key && row.end_time
        )
        .forEach((row, index) => {
          // Calculate hours for this specific task entry
          const workMinutes = row.total_minutes >= row.total_break_minutes
            ? parseInt(row.total_minutes, 10) - parseInt(row.total_break_minutes, 10)
            : parseInt(row.total_break_minutes, 10) - parseInt(row.total_minutes, 10);
          const taskHoursAndMinutes = convertMinutesToHHMM(workMinutes);
          
          reports[key].reportContent += `
            <tr>
              <td style="border: 1px solid #ddd; padding: 8px;">${
                index + 1
              }</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${
                row.project_name || "-"
              }</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${
                row.task_heading || "-"
              }</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${
                (() => {
                  const hours = parseFloat(row.user_task_hours) || 0;
                  const wholeHours = Math.floor(hours);
                  const minutes = Math.round((hours - wholeHours) * 60);
                  return `${wholeHours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
                })()
              }</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${
                row.client_name || "-"
              }</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${
                row.memo || "-"
              }</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${formatTimeTo12Hour(
                row.start_time
              )}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${formatTimeTo12Hour(
                row.end_time
              )}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${taskHoursAndMinutes}</td>
            </tr>`;
        });

      reports[key].reportContent += `
          </table>
          <br>`;

      const projectStatusSections = {
        "In Progress": "2. Work Pending",
        "Sent to Client": "3. Projects With Client",
        "Not Started": "4. Not Started Projects",
      };

      Object.keys(projectStatusSections).forEach((status) => {
        reports[key].reportContent += `
            <h4>${projectStatusSections[status]}</h4>
            <table border="1" style="width: 100%; border-collapse: collapse;">
              <tr style="background-color: #f2f2f2;">
                <th style="border: 1px solid #ddd; padding: 8px;">Sr. No.</th>
                <th style="border: 1px solid #ddd; padding: 8px;">Project Name</th>
                <th style="border: 1px solid #ddd; padding: 8px;">Status</th>
                <th style="border: 1px solid #ddd; padding: 8px;">Client Name</th>
                <th style="border: 1px solid #ddd; padding: 8px;">Start Date</th>
                <th style="border: 1px solid #ddd; padding: 8px;">Due Date</th>
                <th style="border: 1px solid #ddd; padding: 8px;">Other Project Members</th>
                <th style="border: 1px solid #ddd; padding: 8px;">Your Project Hours</th>
                <th style="border: 1px solid #ddd; padding: 8px;">Total Project Hours</th>
              </tr>`;

        const projects = reports[key].pendingProjects[status];
        if (projects && Object.keys(projects).length > 0) {
          Object.keys(projects).forEach((projectId, index) => {
            const project = projects[projectId];
            reports[key].reportContent += `
                <tr>
                  <td style="border: 1px solid #ddd; padding: 8px;">${
                    index + 1
                  }</td>
                  <td style="border: 1px solid #ddd; padding: 8px;">${
                    project.project_name || "-"
                  }</td>
                  <td style="border: 1px solid #ddd; padding: 8px;">${
                    project.status || "-"
                  }</td>
                  <td style="border: 1px solid #ddd; padding: 8px;">${
                    project.client_name || "-"
                  }</td>
                  <td style="border: 1px solid #ddd; padding: 8px;">${
                    project.start_date || "-"
                  }</td>
                  <td style="border: 1px solid #ddd; padding: 8px;">${
                    project.due_date || "-"
                  }</td>
                  <td style="border: 1px solid #ddd; padding: 8px;">${
                    project.members || "-"
                  }</td>
                  <td style="border: 1px solid #ddd; padding: 8px;">${
                    project.user_project_hours || "0.00"
                  }</td>
                  <td style="border: 1px solid #ddd; padding: 8px;">${
                    project.total_project_hours || "0.00"
                  }</td>
                </tr>`;
          });
        } else {
          reports[key].reportContent += `
              <tr>
                <td style="border: 1px solid #ddd; padding: 8px;" colspan="9">No projects found</td>
              </tr>`;
        }

        reports[key].reportContent += `
            </table>
            <br>`;
      });

      reports[key].reportContent += `<br><br>`;
    }
  }

  return reports;
}

cron.schedule("00 22 * * *", async () => {
  console.log("Running email sending task...");
  try {
    const reportData = await getDailyReportContent();

    if (Object.keys(reportData).length === 0) {
      console.log(
        "No reports generated today. No notification email will be sent."
      );
      return;
    }

    let allEmailsSent = true;
    let errorMessage = "";

    for (const key in reportData) {
      const { managerEmail, employeeName, employeeEmail, reportContent } =
        reportData[key];
      if (!isEmailLogged("sent", managerEmail, employeeEmail)) {
        await sendEmailWithRetries(
          reportContent,
          managerEmail,
          employeeName,
          employeeEmail
        );
        if (readLogFile().unsent.length > 0) {
          allEmailsSent = false;
        }
      } else {
        console.log(`(Manager)  -->> Email already sent to: ${managerEmail}`);
        console.log(`(Employee) -->> Email already sent to: ${employeeEmail}`);
      }
    }

    if (allEmailsSent) {
      if (fs.existsSync(logFilePath)) {
        fs.unlinkSync(logFilePath);
        console.log(
          "All mails are sent successfully. Log file deleted successfully."
        );
      }
    } else {
      console.log("Not all emails were sent. Log file retained.");
      errorMessage =
        "Not all emails were sent. Please check the log file for details.";
    }

    await sendNotificationEmail(allEmailsSent, errorMessage);
  } catch (err) {
    console.error("Error occurred:", err);
    await sendNotificationEmail(false, err.message);
  }
});

console.log("Cron job scheduled to run daily at 10:00 PM - New One");

async function sendNotificationEmail(success, errorMessage = "") {
  const transporter = nodemailer.createTransport({
    service: "gmail",
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASSWORD,
    },
  });

  const currentDate = new Date().toLocaleDateString("en-GB", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });

  const mailOptions = {
    from: `"WFMS" <${process.env.SMTP_USER}>`,
    // to: "<EMAIL>",
    to: `<EMAIL>, <EMAIL>`,
    cc: `<EMAIL>`,
    subject: `Daily Email Sending Status - ${currentDate}`,
    html: success
      ? `Dear Sir,<br><br>All daily sheets mails were sent successfully on ${currentDate}.<br><br>Thanks and Regards,<br><br>WFMS Team`
      : `Dear Sir,<br><br>Failed to send some daily sheets mails on ${currentDate}.<br><br>Error details: ${errorMessage}.<br><br>Thanks and Regards,<br><br>WFMS Team`,
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log("Notification email sent successfully.");
  } catch (error) {
    console.error("Failed to send notification email:", error);
  }
}
