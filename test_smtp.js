const nodemailer = require("nodemailer");
require("dotenv").config();

console.log("SMTP Configuration Test");
console.log("=======================");
console.log("SMTP User:", process.env.SMTP_USER);
console.log(
  "SMTP Password:",
  process.env.SMTP_PASSWORD ? "***SET***" : "***NOT SET***"
);
console.log("");

// Test different SMTP configurations
const configs = [
  {
    name: "Gmail Service (Port 465, TLS)",
    config: {
      service: "gmail",
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASSWORD,
      },
      secure: true,
      port: 465,
      connectionTimeout: 30000,
      greetingTimeout: 15000,
      socketTimeout: 30000,
    },
  },
  {
    name: "Gmail Service (Port 587, STARTTLS)",
    config: {
      service: "gmail",
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASSWORD,
      },
      secure: false,
      port: 587,
      connectionTimeout: 30000,
      greetingTimeout: 15000,
      socketTimeout: 30000,
    },
  },
  {
    name: "Explicit Host Configuration (Port 465)",
    config: {
      host: "smtp.gmail.com",
      port: 465,
      secure: true,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASSWORD,
      },
      connectionTimeout: 30000,
      greetingTimeout: 15000,
      socketTimeout: 30000,
    },
  },
  {
    name: "Explicit Host Configuration (Port 587)",
    config: {
      host: "smtp.gmail.com",
      port: 587,
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASSWORD,
      },
      connectionTimeout: 30000,
      greetingTimeout: 15000,
      socketTimeout: 30000,
    },
  },
];

async function testConfig(configName, config) {
  console.log(`Testing: ${configName}`);
  console.log("Config:", JSON.stringify(config, null, 2));

  const transporter = nodemailer.createTransport(config);

  try {
    console.log("Attempting to verify connection...");
    await transporter.verify();
    console.log("✅ SUCCESS: Connection verified!");
    return true;
  } catch (error) {
    console.log("❌ FAILED:", error.message);
    console.log("Error code:", error.code);
    if (error.response) {
      console.log("SMTP response:", error.response);
    }
    return false;
  }
}

async function runTests() {
  console.log("Starting SMTP configuration tests...\n");

  let successCount = 0;

  for (const { name, config } of configs) {
    const success = await testConfig(name, config);
    if (success) successCount++;
    console.log(""); // Empty line for readability
  }

  console.log("Test Summary:");
  console.log(`Successful configurations: ${successCount}/${configs.length}`);

  if (successCount === 0) {
    console.log("\n🔴 All configurations failed. Possible issues:");
    console.log(
      "1. Check your .env file has correct SMTP_USER and SMTP_PASSWORD"
    );
    console.log(
      "2. Ensure you're using an App Password (not regular password)"
    );
    console.log(
      "3. Check if 2-factor authentication is enabled on your Gmail account"
    );
    console.log("4. Verify network connectivity to Gmail servers");
    console.log(
      "5. Check if your firewall/antivirus is blocking outbound connections"
    );
  } else {
    console.log(
      "\n🟢 At least one configuration works. Use the successful one in your main script."
    );
  }
}

// Check if environment variables are set
if (!process.env.SMTP_USER || !process.env.SMTP_PASSWORD) {
  console.error("❌ ERROR: SMTP_USER or SMTP_PASSWORD not set in .env file");
  console.log("Please create a .env file with:");
  console.log("SMTP_USER=<EMAIL>");
  console.log("SMTP_PASSWORD=your-app-password");
  process.exit(1);
}

runTests().catch(console.error);
